"use client";

import { Button } from "@/components/ui/button";
import { FileDown, Users } from "lucide-react";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import axios from "axios";
import { useState } from "react";
import { toast } from "sonner";

export default function ExportSelectedApplicantsButton({ programId }) {
  const [loading, setLoading] = useState(false);

  const handleExport = async () => {
    try {
      setLoading(true);
      toast.info("Fetching data for export...");

      // Fetch the program and applicants based on the provided programId
      const response = await axios.get(`/api/beneficiaries/fetch/export/${programId}`);
      const { program, applicants } = response.data; // Destructure the program and applicants


      toast.success("Data fetched successfully!");

      // If no applicants are found, show an error toast
      if (!Array.isArray(applicants) || applicants.length === 0) {
        toast.error("No selected applicants found.");
        setLoading(false); // Ensure loading is set to false here
        return;
      }

      // Format dates
      const formatDate = (dateString) => {
        if (!dateString) return "-";
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      };

      const startDate = formatDate(program.startDate);
      const endDate = formatDate(program.endDate);

      // Create a new jsPDF instance
      const doc = new jsPDF();

      // Set default font size
      const defaultFontSize = 12;

      // Constants for positioning
      const headerTopMargin = 10;
      const tableTopMargin = 45; // Adjust if header height changes

      // Define header function to add program details to every page
      const addHeader = (docInstance) => { // Renamed doc to docInstance to avoid shadowing
        // Add logo background
        docInstance.setFillColor(0, 0, 100); // Blue background for the logo
        docInstance.circle(28, headerTopMargin + 3, 8, 'F'); // Rounded background

        // Add a simple icon representation using standard characters
        docInstance.setFont("helvetica", "bold");
        docInstance.setTextColor(255, 255, 255); // White text for the icon
        docInstance.setFontSize(14);
        docInstance.text("HM", 24, headerTopMargin + 5); // Using "HM"

        // Reset text color for the title
        docInstance.setTextColor(0);
        docInstance.setFontSize(20);
        docInstance.text("Haruna Maiwada Community Foundation", 38, headerTopMargin + 5);
        docInstance.setFontSize(10);
        docInstance.setTextColor(100);
        docInstance.text(`Program: ${program.name || "-"}`, 14, headerTopMargin + 15);
        docInstance.text(`Date: ${startDate} to ${endDate}`, 14, headerTopMargin + 20);
        docInstance.text(`Venue: ${program.venue || "-"}`, 14, headerTopMargin + 25); // Added default '-'
        docInstance.setTextColor(0);
        docInstance.setFontSize(16);
        docInstance.text("List Of Selected Applicants ", 70, headerTopMargin + 32);
        docInstance.setFontSize(defaultFontSize);
      };

      toast.info("Generating PDF...");

      // Generate the table for applicants with header on each page
      autoTable(doc, {
        startY: tableTopMargin,
        // --- MODIFICATION START ---
        head: [["S/N", "Name", "Phone", "Ward"]], // Added "S/N" header
        body: applicants.map((applicant, index) => [
          index + 1, // Add row number (starting from 1)
          applicant.name || "-",
          applicant.phone || "-",
          applicant.ward || "-",
          
        ]),
        // --- MODIFICATION END ---
        didDrawPage: (data) => {
          // Add header to each new page created by autoTable
          // Ensure header is added *before* the table on the first page too
          // autoTable calls this *after* drawing the page content
          addHeader(doc); // Pass the doc instance
        },
        // Set consistent margins for all pages
        margin: { top: tableTopMargin },
        // Ensure there's space at top for our header
        tableWidth: 'auto',
        styles: { overflow: 'linebreak' },
        headStyles: { fillColor: [0, 0, 100] } // Blue header background
        // Optional: Style the first column (S/N) if needed
        // columnStyles: {
        //   0: { halign: 'center', cellWidth: 15 } // Center align S/N and set a fixed width
        // }
      });

      

      // Save the PDF as a file
      const fileName = `Beneficiaries For ${program.name || 'Program'}.pdf`;
      doc.save(fileName.replace(/[/\\?%*:|"<>]/g, '-')); // Sanitize filename


      toast.success(`Successfully exported ${applicants.length} beneficiaries to PDF!`, {
        duration: 5000
      });
    } catch (error) {
      console.error("Failed to export PDF:", error);
      toast.error("Error exporting applicants", {
        description: error.message || "Please try again later",
        duration: 5000
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div // Changed to div as Button component doesn't accept onClick directly in this setup
      onClick={!loading ? handleExport : undefined} // Prevent click when loading
      // Using Button styling via className
      className={`btn ml-3 text-white bg-card border rounded-lg w-60 hover:bg-blue-900 border-slate-700 flex items-center justify-center cursor-pointer ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
      aria-disabled={loading} // Accessibility
    >
      <FileDown size={18} className="mr-2" /> {/* Added margin */}
      {loading ? "Exporting..." : "Export Beneficiaries"}
    </div>
  );
}