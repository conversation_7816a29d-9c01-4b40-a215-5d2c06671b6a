import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";
import { wards } from "@/lib/wards";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

// Create a custom logger for this route
const logger = createLogger({ service: "ward-percentage-selection" });

// Define the main handler function
async function selectApplicantsByWardPercentageHandler(req, { params }) {
  try {
    await dbConnect();
    
    // Await params first (Next.js 15 requirement)
    const awaitedParams = await params;
    const { programId } = awaitedParams;
    
    // Parse request body to get ward percentages
    const body = await req.json();
    const { wardPercentages } = body;
    
    logger.info("Starting ward-based percentage selection", { 
      programId, 
      wardPercentages 
    });

    // Validate ward percentages
    if (!wardPercentages || typeof wardPercentages !== 'object') {
      return NextResponse.json({ 
        error: "Ward percentages are required" 
      }, { status: 400 });
    }

    // Validate that percentages add up to 100%
    const totalPercentage = Object.values(wardPercentages).reduce((sum, pct) => sum + pct, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      return NextResponse.json({ 
        error: "Ward percentages must add up to 100%" 
      }, { status: 400 });
    }

    const program = await Program.findById(programId);
    if (!program) {
      logger.warn("Program not found", { programId });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    if (program.selectionProcess) {
      logger.warn("Selection already processed", { programId });
      return NextResponse.json(
        { error: "Applicants have already been selected for this program." },
        { status: 400 }
      );
    }

    const programForm = await ProgramForm.findOne({ programid: programId });
    logger.info("Found program form", { formId: programForm?._id });

    const applicants = await Applicant.find({ programId, status: "approved" });
    if (applicants.length === 0) {
      logger.warn("No approved applicants found", { programId });
      return NextResponse.json(
        { error: "No approved applicants found for selection." },
        { status: 400 }
      );
    }

    logger.info("Found approved applicants", { count: applicants.length });

    // Group applicants by ward
    const wardGroups = Object.fromEntries(wards.map(ward => [ward, []]));
    applicants.forEach((applicant) => {
      const ward = applicant.formData.get("ward");
      if (wards.includes(ward)) {
        wardGroups[ward].push(applicant);
      }
    });

    const totalSlots = program.maxBeneficiaries;
    const selectedApplicants = [];
    const selectionSummary = {};

    // Select applicants based on ward percentages
    for (const ward of wards) {
      const percentage = wardPercentages[ward] || 0;
      const wardSlots = Math.round((percentage / 100) * totalSlots);
      const availableApplicants = wardGroups[ward];
      
      // Randomly shuffle applicants in this ward
      const shuffled = availableApplicants.sort(() => 0.5 - Math.random());
      const selectCount = Math.min(wardSlots, shuffled.length);
      
      // Select the required number of applicants
      const selectedFromWard = shuffled.slice(0, selectCount);
      selectedApplicants.push(...selectedFromWard);
      
      selectionSummary[ward] = {
        percentage: percentage,
        allocatedSlots: wardSlots,
        availableApplicants: availableApplicants.length,
        selectedCount: selectCount
      };
      
      logger.info(`Ward ${ward} selection complete`, { 
        ward, 
        percentage,
        allocatedSlots: wardSlots,
        selected: selectCount, 
        available: availableApplicants.length 
      });
    }

    // Handle any remaining slots due to rounding or unavailable applicants
    const actualSelected = selectedApplicants.length;
    const remainingSlots = totalSlots - actualSelected;

    if (remainingSlots > 0) {
      // Get all unselected applicants ONLY from wards that have percentage > 0
      const selectedIds = new Set(selectedApplicants.map(a => a._id.toString()));
      const unselectedPool = [];

      // Only include applicants from wards with percentage > 0
      for (const ward of wards) {
        const percentage = wardPercentages[ward] || 0;
        if (percentage > 0) {
          const wardApplicants = wardGroups[ward].filter(a => !selectedIds.has(a._id.toString()));
          unselectedPool.push(...wardApplicants);
        }
      }

      if (unselectedPool.length > 0) {
        // Randomly select from remaining pool (only from wards with percentage > 0)
        const shuffledUnselected = unselectedPool.sort(() => 0.5 - Math.random());
        const additionalSelected = shuffledUnselected.slice(0, remainingSlots);
        selectedApplicants.push(...additionalSelected);

        logger.info("Filled remaining slots", {
          remainingSlots,
          additionalSelected: additionalSelected.length,
          filledFrom: "wards with percentage > 0 only"
        });
      } else {
        logger.warn("No remaining applicants available from wards with percentage > 0", {
          remainingSlots
        });
      }
    }

    // Update selected applicants in DB
    const selectedIds = selectedApplicants.map(a => a._id);
    await Applicant.updateMany(
      { _id: { $in: selectedIds } },
      { $set: { status: "selected" } }
    );
    
    logger.info("Updated applicant statuses", { 
      selectedCount: selectedIds.length 
    });

    // Finalize the form and program
    programForm.status = "closed";
    await programForm.save();

    program.selectionProcess = true;
    await program.save();
    
    logger.info("Ward-based percentage selection completed successfully", {
      programId,
      selectedCount: selectedApplicants.length,
      totalSlots,
      selectionSummary
    });

    return NextResponse.json({
      message: "Applicants selected successfully using ward percentages",
      selectedApplicants,
      selectionSummary,
      totalSelected: selectedApplicants.length,
      totalSlots
    }, { status: 200 });

  } catch (error) {
    logger.error("Error in ward-based percentage selection", {
      error: error.message,
      stack: error.stack
    });
    
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Apply rate limiting - 5 requests per minute
const wardPercentageSelectionRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requests per minute
  keyPrefix: "ward-percentage-selection"
});

// Export the secured POST handler with rate limiting and admin auth
export async function POST(req, { params }) {
  // Apply rate limiting first
  const rateLimitResult = await wardPercentageSelectionRateLimit(req);
  if (!rateLimitResult.success) {
    logger.warn("Rate limit exceeded", {
      path: req.nextUrl.pathname,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return rateLimitResult.response;
  }
  
  // Then apply admin authentication
  return withAdminAuth(selectApplicantsByWardPercentageHandler)(req, { params });
}
