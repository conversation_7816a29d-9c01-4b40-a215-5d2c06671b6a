import { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { wards } from '@/lib/wards';

function SelectProgramButton({ programId }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showWardPercentages, setShowWardPercentages] = useState(false);
  const [wardPercentages, setWardPercentages] = useState({});
  const [wardStats, setWardStats] = useState([]);
  const [loadingWardStats, setLoadingWardStats] = useState(false);
  const queryClient = useQueryClient();

  // Fetch ward statistics when modal opens
  const fetchWardStats = async () => {
    setLoadingWardStats(true);
    try {
      const response = await axios.get(`/api/programs/ward-stats/${programId}`);
      if (response.data.success) {
        setWardStats(response.data.data.wardStats);

        // Initialize ward percentages with equal distribution
        const equalPercentage = Math.round(100 / wards.length * 100) / 100;
        const initialPercentages = {};
        wards.forEach(ward => {
          initialPercentages[ward] = equalPercentage;
        });

        // Adjust the last ward to make total exactly 100%
        const totalInitial = Object.values(initialPercentages).reduce((sum, pct) => sum + pct, 0);
        const lastWard = wards[wards.length - 1];
        initialPercentages[lastWard] = Math.round((initialPercentages[lastWard] + (100 - totalInitial)) * 100) / 100;

        setWardPercentages(initialPercentages);
      }
    } catch (error) {
      console.error('Error fetching ward stats:', error);
      toast.error('Failed to load ward statistics');
    } finally {
      setLoadingWardStats(false);
    }
  };

  // Handle percentage change for a ward
  const handlePercentageChange = (ward, value) => {
    const numValue = parseFloat(value) || 0;
    setWardPercentages(prev => ({
      ...prev,
      [ward]: numValue
    }));
  };

  // Auto-distribute remaining percentage
  const autoDistribute = () => {
    const equalPercentage = Math.round(100 / wards.length * 100) / 100;
    const newPercentages = {};
    wards.forEach(ward => {
      newPercentages[ward] = equalPercentage;
    });

    // Adjust the last ward to make total exactly 100%
    const totalInitial = Object.values(newPercentages).reduce((sum, pct) => sum + pct, 0);
    const lastWard = wards[wards.length - 1];
    newPercentages[lastWard] = Math.round((newPercentages[lastWard] + (100 - totalInitial)) * 100) / 100;

    setWardPercentages(newPercentages);
  };

  // Calculate total percentage
  const totalPercentage = Object.values(wardPercentages).reduce((sum, pct) => sum + pct, 0);

  const handleConfirmSelection = async (useWardPercentages = false) => {
    setIsLoading(true);

    try {
      let response;

      if (useWardPercentages) {
        // Validate percentages add up to 100%
        if (Math.abs(totalPercentage - 100) > 0.01) {
          toast.error('Ward percentages must add up to 100%');
          setIsLoading(false);
          return;
        }

        response = await axios.post(`/api/selection/ward-percentage/${programId}`, {
          wardPercentages
        });
      } else {
        response = await axios.post(`/api/selection/random/${programId}`);
      }

      if (response.status === 200) {
        toast.success(response.data.message || 'Selection successful');

        // Invalidate and immediately refetch the program details query
        await queryClient.invalidateQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.invalidateQueries({
          queryKey: [ "ward-stats"],
        });

        await queryClient.invalidateQueries({
          queryKey: ['beneficiaries'],
        });

        await queryClient.refetchQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.refetchQueries({
          queryKey:["programApplicants", programId],
        });

      } else {
        toast.error(`${response.data.error || 'Unexpected error occurred'}`);
      }
    } catch (error) {
      if (error.response) {
        toast.error(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else {
        toast.error('Network Error: Failed to select applicants');
      }
    } finally {
      setIsLoading(false);
      setIsModalOpen(false);
      setShowWardPercentages(false);
    }
  };
  

  return (
    <div>
      <button
        className="btn bg-card border rounded-lg w-60 hover:bg-blue-900 border-slate-700"
        onClick={() => {
          setIsModalOpen(true);
          fetchWardStats();
        }}
      >
        Initiate Selection Process
      </button>

      {isModalOpen && (
        <dialog className="modal modal-open">
          <div className="modal-box bg-card border border-slate-700 text-white max-w-4xl">
            <button
              className="btn btn-sm btn-circle absolute top-2 right-2 text-gray-400 hover:text-white"
              onClick={() => {
                setIsModalOpen(false);
                setShowWardPercentages(false);
              }}
            >
              &times;
            </button>

            <h2 className="text-xl text-center mb-4 font-bold">Applicant Selection</h2>

            {!showWardPercentages ? (
              // Initial selection method choice
              <div className="space-y-4">
                <p className="text-center">Choose your selection method:</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Random Selection */}
                  <div className="border border-slate-600 rounded-lg p-4">
                    <h3 className="font-bold mb-2">Random Selection</h3>
                    <p className="text-sm text-gray-300 mb-4">
                      Equal distribution across all wards with random selection within each ward.
                    </p>
                    <button
                      className="btn bg-blue-900 hover:bg-blue-700 text-white w-full"
                      onClick={() => handleConfirmSelection(false)}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Processing...' : 'Use Random Selection'}
                    </button>
                  </div>

                  {/* Ward Percentage Selection */}
                  <div className="border border-slate-600 rounded-lg p-4">
                    <h3 className="font-bold mb-2">Ward-Based Percentage</h3>
                    <p className="text-sm text-gray-300 mb-4">
                      Set custom percentages for each ward based on applicant distribution.
                    </p>
                    <button
                      className="btn bg-green-700 hover:bg-green-600 text-white w-full"
                      onClick={() => setShowWardPercentages(true)}
                      disabled={loadingWardStats}
                    >
                      {loadingWardStats ? 'Loading...' : 'Configure Ward Percentages'}
                    </button>
                  </div>
                </div>

                <p className="text-sm text-center font-bold text-red-400 mt-4">
                  ⚠️ Once selection is done, it cannot be reversed!
                </p>
              </div>
            ) : (
              // Ward percentage configuration
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <button
                    className="btn btn-sm bg-gray-700 hover:bg-gray-600"
                    onClick={() => setShowWardPercentages(false)}
                  >
                    ← Back
                  </button>
                  <div className="text-center">
                    <span className={`font-bold ${Math.abs(totalPercentage - 100) < 0.01 ? 'text-green-400' : 'text-red-400'}`}>
                      Total: {totalPercentage.toFixed(1)}%
                    </span>
                  </div>
                  <button
                    className="btn btn-sm bg-blue-700 hover:bg-blue-600"
                    onClick={autoDistribute}
                  >
                    Auto Distribute
                  </button>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {wardStats.map((stat) => (
                      <div key={stat.ward} className="border border-slate-600 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-sm">{stat.ward}</span>
                          <span className="text-xs text-gray-400">
                            {stat.count} applicants ({stat.percentage}%)
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            value={wardPercentages[stat.ward] || 0}
                            onChange={(e) => handlePercentageChange(stat.ward, e.target.value)}
                            className="input input-sm bg-gray-800 border-gray-600 text-white flex-1"
                          />
                          <span className="text-sm">%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="modal-action">
                  <button
                    className="btn bg-gray-800 border-none rounded-lg hover:bg-red-700"
                    onClick={() => {
                      setIsModalOpen(false);
                      setShowWardPercentages(false);
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn border-none bg-green-700 rounded-lg hover:bg-green-600"
                    onClick={() => handleConfirmSelection(true)}
                    disabled={isLoading || Math.abs(totalPercentage - 100) > 0.01}
                  >
                    {isLoading ? 'Processing...' : 'Confirm Ward-Based Selection'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </dialog>
      )}
    </div>
  );
}

export default SelectProgramButton;
