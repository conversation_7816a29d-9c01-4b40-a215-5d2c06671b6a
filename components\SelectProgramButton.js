import { useState } from 'react';
import axios from 'axios';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

function SelectProgramButton({ programId }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  const handleConfirmSelection = async () => {
    setIsLoading(true);
  
    try {
      const response = await axios.post(`/api/selection/random/${programId}`);
  
      if (response.status === 200) {
        toast.success(response.data.message || 'Selection successful');
  
        // Invalidate and immediately refetch the program details query
        await queryClient.invalidateQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.invalidateQueries({
          queryKey: [ "ward-stats"],
        });
         
        await queryClient.invalidateQueries({
          queryKey: ['beneficiaries'],
        });
  
        await queryClient.refetchQueries({
          queryKey: ['programDetails', programId],
        });
        await queryClient.refetchQueries({
          queryKey:["programApplicants", programId],
        });
         
      } else {
        toast.error(`${response.data.error || 'Unexpected error occurred'}`);
      }
    } catch (error) {
      if (error.response) {
        toast.error(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else {
        toast.error('Network Error: Failed to select applicants');
      }
    } finally {
      setIsLoading(false);
      setIsModalOpen(false);
    }
  };
  

  return (
    <div>
      <button
        className="btn bg-card border rounded-lg w-60 hover:bg-blue-900 border-slate-700"
        onClick={() => setIsModalOpen(true)}
      >
        Initiate Selection Process
      </button>

      {isModalOpen && (
        <dialog className="modal modal-open">
          <div className="modal-box bg-card border border-slate-700 text-white">
            <button
              className="btn btn-sm btn-circle absolute top-2 right-2 text-gray-400 hover:text-white"
              onClick={() => setIsModalOpen(false)}
            >
              &times;
            </button>
            <h2 className="text-xl text-center mb-2 font-bold">Confirm Selection</h2>
            <p className=''>Are you sure you want to select applicants for this program?</p>
            <p className='text-sm text-center font-bold text-blue-600'>once selection has been done it cannot be reversed !</p>
            <div className="modal-action">
              <button
                className="btn bg-gray-800  border-none rounded-lg hover:bg-red-700"
                onClick={() => setIsModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="btn border-none bg-blue-900 rounded-lg hover:bg-blue-700"
                onClick={handleConfirmSelection}
                disabled={isLoading}
              >
                {isLoading ? 'Processing...' : 'Confirm'}
              </button>
            </div>
          </div>
        </dialog>
      )}
    </div>
  );
}

export default SelectProgramButton;
