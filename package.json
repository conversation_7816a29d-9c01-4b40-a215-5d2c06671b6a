{"name": "harunacomm<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@logtail/node": "^0.5.4", "@logtail/winston": "^0.5.4", "@next/third-parties": "^15.1.8", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.71.10", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.8", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "daisyui": "^5.0.9", "express-rate-limit": "^7.5.0", "formik": "^2.4.6", "fs": "^0.0.1-security", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.484.0", "mongoose": "^8.13.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-rate-limit": "^0.0.3", "next-themes": "^0.4.6", "path": "^0.12.7", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.2", "slugify": "^1.6.6", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "winston-mongodb": "^6.0.0", "xss": "^1.0.15", "zod": "^3.24.2", "zod-formik-adapter": "^1.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}