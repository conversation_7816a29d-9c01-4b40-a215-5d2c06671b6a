import mongoose from "mongoose";

const ApplicantSchema = new mongoose.Schema(
  {
    programId: {
      type: String, // Stores the ID of the related charity program
      required: true,
    },
    formPhase: {
      type: Number, // Tracks the phase in which the applicant applied
      required: true, // Must be explicitly set based on the current ProgramForm phase
    },
    formData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed, // Allows dynamic form fieldsa
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "selected"], // Status tracking
      default: "pending",
    },
    reviewerId: {
      type: String,
      default: null, // Default value is null
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

export default mongoose.models.Applicant || mongoose.model("Applicant", ApplicantSchema);
