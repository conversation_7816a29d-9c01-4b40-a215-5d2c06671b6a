import { wards } from "@/lib/wards"; // Assuming you have wards data

// Function to generate dummy applicants
export const generateDummyApplicants = (num) => {
  const applicants = [];
  const statuses = ["approved", "rejected",];

  for (let i = 0; i < num; i++) {
    const ward = wards[Math.floor(Math.random() * wards.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    const applicant = {
      programId: `6821c3f84cac5d48a0a484e2`,
      formPhase: 1, // Form phase (1-3)
      status: status,
      formData: {
        name: `Applicant ${i + 1}`,
        email: `applicant${i + 1}@example.com`,
        phonenumber: `0706${Math.floor(Math.random() * *********)}`,
        state: "Katsina", // You can randomize this as well if you like
        ward: ward,
        "jamb-slip": {
          size: Math.floor(Math.random() * 10000000), // Random file size
          type: "image/png", // Just using a default type for testing
          name: `IMG_${i + 1}.png`,
          lastModified: new Date().getTime(),
        },
        passport: {
          size: Math.floor(Math.random() * 1000000),
          type: "image/jpeg",
          name: `${Math.random().toString(36).substring(2, 15)}.jpeg`,
          lastModified: new Date().getTime(),
        },
        programid: '67eb28f2a9be9eea5f271d70',
      },
      createdAt: new Date(),
    };
    applicants.push(applicant);
  }

  return applicants;
};
