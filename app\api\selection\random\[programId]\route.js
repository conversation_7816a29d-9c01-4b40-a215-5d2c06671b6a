import { NextResponse } from "next/server";
import { dbConnect } from "@/lib/db";
import Applicant from "@/models/Applicant";
import Program from "@/models/Program";
import ProgramForm from "@/models/ProgramForm";
import { wards } from "@/lib/wards";
import { withAdminAuth } from "@/middleware/authMiddleware";
import { rateLimit } from "@/lib/ratelimit";
import { createLogger } from "@/lib/logger";

// Create a custom logger for this route
const logger = createLogger({ service: "program-applicant-selection" });

// Define the main handler function
async function selectApplicantsHandler(req, { params }) {
  try {
    await dbConnect();
    const { programId } = params;
    
    logger.info("Starting applicant selection process", { programId });

    const program = await Program.findById(programId);
    if (!program) {
      logger.warn("Program not found", { programId });
      return NextResponse.json({ error: "Program not found" }, { status: 404 });
    }

    if (program.selectionProcess) {
      logger.warn("Selection already processed", { programId });
      return NextResponse.json(
        { error: "Applicants have already been selected for this program." },
        { status: 400 }
      );
    }

    const programForm = await ProgramForm.findOne({ programid: programId });
    logger.info("Found program form", { formId: programForm?._id });

    const applicants = await Applicant.find({ programId, status: "approved" });
    if (applicants.length === 0) {
      logger.warn("No approved applicants found", { programId });
      return NextResponse.json(
        { error: "No approved applicants found for selection." },
        { status: 400 }
      );
    }

    logger.info("Found approved applicants", { count: applicants.length });

    // Group applicants by ward
    const wardGroups = Object.fromEntries(wards.map(ward => [ward, []]));
    applicants.forEach((applicant) => {
      const ward = applicant.formData.get("ward");
      if (wards.includes(ward)) {
        wardGroups[ward].push(applicant);
      }
    });

    const totalSlots = program.maxBeneficiaries;
    const baseSlotsPerWard = Math.floor(totalSlots / wards.length);
    let remainingSlots = totalSlots;

    const selectedApplicants = [];

    // First pass: assign base slots
    for (const ward of wards) {
      const shuffled = wardGroups[ward].sort(() => 0.5 - Math.random());
      const selectCount = Math.min(baseSlotsPerWard, shuffled.length);
      selectedApplicants.push(...shuffled.slice(0, selectCount));
      remainingSlots -= selectCount;
      
      logger.info(`Ward ${ward} selection complete`, { 
        ward, 
        selected: selectCount, 
        available: shuffled.length 
      });
    }

    // Second pass: fill remaining slots from wards with leftovers
    if (remainingSlots > 0) {
      // Get all unselected applicants across all wards
      const unselectedPool = [];
      for (const ward of wards) {
        const alreadySelectedIds = new Set(selectedApplicants.map(a => a._id.toString()));
        const unselected = wardGroups[ward].filter(a => !alreadySelectedIds.has(a._id.toString()));
        unselectedPool.push(...unselected);
      }

      const shuffledUnselected = unselectedPool.sort(() => 0.5 - Math.random());
      selectedApplicants.push(...shuffledUnselected.slice(0, remainingSlots));
      
      logger.info("Filled remaining slots", { 
        remainingSlots, 
        filledFrom: "unselected pool" 
      });
    }

    // Update selected applicants in DB
    const selectedIds = selectedApplicants.map(a => a._id);
    await Applicant.updateMany(
      { _id: { $in: selectedIds } },
      { $set: { status: "selected" } }
    );
    
    logger.info("Updated applicant statuses", { 
      selectedCount: selectedIds.length 
    });

    // Finalize the form and program
    programForm.status = "closed";
    await programForm.save();

    program.selectionProcess = true;
    await program.save();
    
    logger.info("Selection process completed successfully", {
      programId,
      selectedCount: selectedApplicants.length,
      totalSlots
    });

    return NextResponse.json({
      message: "Applicants selected successfully",
      selectedApplicants,
    }, { status: 200 });

  } catch (error) {
    logger.error("Error in applicant selection process", {
      error: error.message,
      stack: error.stack
    });
    
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Apply rate limiting - 5 requests per minute
const applicantSelectionRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requests per minute
  keyPrefix: "applicant-selection"
});

// Export the secured POST handler with rate limiting and admin auth
export async function POST(req, { params }) {
  // Apply rate limiting first
  const rateLimitResult = await applicantSelectionRateLimit(req);
  if (!rateLimitResult.success) {
    logger.warn("Rate limit exceeded", {
      path: req.nextUrl.pathname,
      ip: req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || "unknown"
    });
    return rateLimitResult.response;
  }
  
  // Then apply admin authentication
  return withAdminAuth(selectApplicantsHandler)(req, { params });
}