"use client";

import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { RefreshCcw} from "lucide-react";
import { ProgramOverview } from "@/components/ProgramDetailsComp";
import ProgramEditForm from "@/components/ProgramEditForm";
import DrawerLayout from "@/components/DrawerLayout";
import DynamicFormEditor from "@/components/DynamicFormEditor";
import ProgramApplicantsTable from "@/components/ProgramApplicantsTable";
import SelectProgramButton from "@/components/SelectProgramButton";
import ExportSelectedApplicantsButton from "@/components/ExportSelectedApplicantsButton";
import AdminOnly from "@/components/AdminOnly";
import AuthOnly from "@/components/AuthOnly";
import { ProgramOverviewSkeleton } from "@/components/ProgramOverviewSkeleton";
const fetchProgramDetails = async (id) => {
  const { data } = await axios.get(`/api/programs/completedetails/${id}`);
  return data;
};

export default function ProgramDetails() {
  const { id } = useParams();

  const {
    data,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["programDetails", id],
    queryFn: () => fetchProgramDetails(id),
    enabled: !!id, 
    staleTime: 5 * 60 * 1000,// Prevent query from running until `id` is available
  });

  if (isLoading) {
    return (
      <DrawerLayout>
        <div className="flex px-5  items-center justify-center h-screen">
            <ProgramOverviewSkeleton/>
           </div>
      </DrawerLayout>
    );
  }

  if (isError) {
    return (
      <DrawerLayout>
        <div className="text-center text-red-500">
          {error?.message || "Failed to fetch program details"}
        </div>
      </DrawerLayout>
    );
  }

  return (
    <AuthOnly>
    <DrawerLayout>
      <div className="p-4 md:p-6 w-full min-h-screen space-y-6">
      <div className="flex overflow-x-auto overflow-y-hidden scroll-hidden">
   
      <AdminOnly>
  <ProgramEditForm program={data.program} />
  <DynamicFormEditor programid={data.program._id} initialProgramData={data.programForm} />
  <SelectProgramButton programId={data.program._id} />
</AdminOnly>
  <ExportSelectedApplicantsButton programId={data.program._id} />

  <style jsx>{`
    .scroll-hidden {
      scrollbar-width: none; 
      -ms-overflow-style: none; 
    }
    .scroll-hidden::-webkit-scrollbar {
      display: none;
    }
  `}</style>
</div>


        <ProgramOverview program={data.program} programForm={data.programForm} />

        <h2 className="text-2xl text-center font-bold mb-4">Program Applicants</h2>
        <ProgramApplicantsTable programId={data.program._id} />
      </div>
    </DrawerLayout></AuthOnly>
  );
}
