import { useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { Trash2, AlertTriangle } from 'lucide-react';

function DeleteProgramButton({ programId, programName }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const queryClient = useQueryClient();
  const router = useRouter();

  const handleDeleteProgram = async () => {
    // Require user to type "DELETE" to confirm
    if (confirmText !== 'DELETE') {
      toast.error('Please type "DELETE" to confirm');
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await axios.delete(`/api/programs/delete/${programId}`);
      
      toast.success('Program deleted successfully', {
        description: 'The program and all related data have been permanently deleted.'
      });

      // Invalidate relevant queries
      await queryClient.invalidateQueries({ queryKey: ['programs'] });
      await queryClient.invalidateQueries({ queryKey: ['programDetails', programId] });
      
      // Navigate back to programs list after a short delay
      setTimeout(() => {
        router.push('/programs');
      }, 2000);

    } catch (error) {
      console.error('Error deleting program:', error);
      
      const errorMessage = error.response?.data?.error || 'Failed to delete program';
      toast.error('Delete failed', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
    setConfirmText('');
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setConfirmText('');
  };

  return (
    <>
      {/* Delete Button */}
      <button
        className="btn bg-red-600 hover:bg-red-700 text-white border-none rounded-lg transition-colors flex items-center gap-2"
        onClick={openModal}
      >
        <Trash2 size={16} />
        Delete Program
      </button>

      {/* Warning Modal */}
      {isModalOpen && (
        <dialog className="modal modal-open">
          <div className="modal-box bg-card border border-red-500 text-white max-w-md">
            <button
              className="btn btn-sm btn-circle absolute top-2 right-2 text-gray-400 hover:text-white"
              onClick={closeModal}
              disabled={isLoading}
            >
              &times;
            </button>
            
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="text-red-500" size={24} />
              <h2 className="text-xl font-bold text-red-500">Delete Program</h2>
            </div>
            
            <div className="space-y-4">
              <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <p className="text-sm font-semibold text-red-400 mb-2">⚠️ WARNING: This action cannot be undone!</p>
                <p className="text-sm text-gray-300">
                  Deleting this program will permanently remove:
                </p>
                <ul className="text-sm text-gray-300 mt-2 ml-4 list-disc">
                  <li>The program: <span className="font-semibold text-white">"{programName}"</span></li>
                  <li>All program applicants and their data</li>
                  <li>All selected beneficiaries</li>
                  <li>The program's application form</li>
                  <li>All related records and files</li>
                </ul>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Type <span className="font-bold text-red-400">"DELETE"</span> to confirm:
                </label>
                <input
                  type="text"
                  value={confirmText}
                  onChange={(e) => setConfirmText(e.target.value)}
                  className="input input-bordered w-full bg-gray-800 border-gray-600 text-white"
                  placeholder="Type DELETE here"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="modal-action mt-6">
              <button
                className="btn bg-gray-600 hover:bg-gray-700 text-white border-none rounded-lg"
                onClick={closeModal}
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                className={`btn border-none rounded-lg text-white ${
                  confirmText === 'DELETE' 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-gray-500 cursor-not-allowed'
                }`}
                onClick={handleDeleteProgram}
                disabled={isLoading || confirmText !== 'DELETE'}
              >
                {isLoading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} />
                    Delete Program
                  </>
                )}
              </button>
            </div>
          </div>
        </dialog>
      )}
    </>
  );
}

export default DeleteProgramButton;
