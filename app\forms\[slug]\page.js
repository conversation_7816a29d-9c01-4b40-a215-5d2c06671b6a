"use client"

import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Formik, Form, Field, ErrorMessage } from "formik"
import { toFormikValidate } from "zod-formik-adapter"
import { generateFormSchema } from "@/lib/generateFormSchema"
import axios from "axios"
import { toast } from "sonner"
import slugify from "slugify"
import { useQueryClient } from "@tanstack/react-query"
import { Loader2, RefreshCcw, AlertCircle, FileText, Send } from "lucide-react"
import Header1 from "@/components/Header1"
import ProgramDetailsCard from "@/components/ProgramDetailsCard"
import Footer from "@/components/Footer"
const normalizeLabel = (label) => slugify(label, { lower: true, strict: true })

export default function FormPage() {
  const params = useParams()
  const slug = params?.slug

  const [form, setForm] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const queryClient = useQueryClient()

  useEffect(() => {
    if (!slug) return

    async function fetchForm() {
      try {
        const response = await axios.get(`/api/programforms/${slug}`)
        setForm(response.data.form)
      } catch (err) {
        setError(err.response?.data?.error || "Failed to fetch form")
      } finally {
        setLoading(false)
      }
    }

    fetchForm()
  }, [slug])

  if (!slug) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/80">
        <div className="p-8 rounded-xl bg-card border border-red-500/20 shadow-lg animate-fade-in">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-center text-red-500">Invalid Form URL</h2>
          <p className="text-muted-foreground text-center mt-2">The form you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/80">
        <div className="flex flex-col items-center animate-fade-in">
          <RefreshCcw className="animate-spin text-blue-500" size={48} />
          <h2 className="text-xl font-medium text-center">Loading Application Form</h2>
          <p className="text-muted-foreground text-center mt-2">Please wait while we prepare your form...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/80">
        <div className="p-8 rounded-xl bg-card border border-red-500/20 shadow-lg max-w-md w-full animate-fade-in">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-center text-red-500">Error Loading Form</h2>
          <p className="text-muted-foreground text-center mt-2">{error}</p>
        </div>
      </div>
    )
  }

  const initialValues = form.fields.reduce((acc, field) => {
    const normalizedLabel = normalizeLabel(field.label)
    acc[normalizedLabel] = field.inputType === "file" ? null : ""
    return acc
  }, {})

  const formSchema = generateFormSchema(form.fields.map((field) => ({ ...field, label: normalizeLabel(field.label) })))

  const onSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true)
    try {
      const formData = new FormData()
      Object.entries(values).forEach(([key, value]) => {
        if (value instanceof File) {
          formData.append(key, value)
        } else {
          formData.append(key, value)
        }
      })
      formData.append("programid", form.programid)

      await axios.post("/api/applicants/create", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      })

      toast.success("Application submitted successfully! ✅")
      resetForm() // Reset the form to initial values
      await queryClient.invalidateQueries(["programApplicants", form.programid])
      await queryClient.invalidateQueries(["applicants"])
    } catch (error) {
      toast.error(error.response?.data?.error || "Submission failed ❌")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen text-white bg-gradient-to-b from-background to-background/90">
      <Header1 />

      <div className="container mx-auto px-4 py-6 animate-slide-up">
        <div className="flex flex-col items-center ">
          <h2 className="font-mono text-lg md:text-2xl font-extrabold text-center text-black dark:text-white">
            APPLICATION PORTAL
          </h2>
        </div>

        <ProgramDetailsCard programId={form.programid} />

        <div className="max-w-3xl mx-auto mt-6 mb-16 animate-fade-in-delayed">
          <div className="bg-card border border-slate-700/50 rounded-2xl shadow-xl overflow-hidden">
            <div className="p-6 md:p-8 dark:bg-slate-950 border-b border-slate-700/50 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
              <div className="flex items-center gap-3 mb-4">
                <FileText className="h-6 w-6" />
                <h1 className="text-2xl font-bold">{form.title}</h1>
              </div>
              <p className="text-white">{form.description}</p>
            </div>

            <div className="p-4 md:p-8">
              <Formik initialValues={initialValues} validate={toFormikValidate(formSchema)} onSubmit={onSubmit}>
                {({ setFieldValue, resetForm }) => (
                  <Form className="space-y-8">
                    <div className="bento-grid grid grid-cols-1 md:grid-cols-2 gap-6">
                      {form.fields?.map((field, index) => {
                        const normalizedLabel = normalizeLabel(field.label)
                        return (
                          <div
                            key={field._id || index}
                            className={`form-control p-5 rounded-xl border-2 border-slate-700/50 bg-slate-800/30 dark:bg-slate-950 backdrop-blur-sm hover:bg-slate-800/50 transition-all duration-300 animate-fade-in-staggered ${
                              field.inputType === "file" || field.inputType === "textarea" ? "md:col-span-2" : ""
                            }`}
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            <label className="block mb-2">
                              <span className="font-medium text-sm">{field.label}</span>
                              {field.inputType === "file" && (
                                <span className="text-xs text-blue-300 ml-2">(Max: 2MB)</span>
                              )}
                            </label>

                            {field.inputType === "select" ? (
                              <div className="relative">
                                <Field
                                  as="select"
                                  name={normalizedLabel}
                                  className="w-full p-3 text-black dark:text-white rounded-lg bg-background border border-slate-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-all duration-200"
                                >
                                  <option value="">Select {field.label}</option>
                                  {field.options?.map((option, idx) => (
                                    <option key={idx}>{option}</option>
                                  ))}
                                </Field>
                              </div>
                            ) : field.inputType === "file" ? (
                              <div className="relative">
                                <input
                                  type="file"
                                  className="w-full text-black dark:text-white p-3 rounded-lg bg-background border border-slate-700 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary/90 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-all duration-200"
                                  onChange={(e) => {
                                    const file = e.target.files[0];
                                    if (file && file.size > 2 * 1024 * 1024) {
                                      toast.error(`File size exceeds 2MB limit`);
                                      e.target.value = "";
                                      setFieldValue(normalizedLabel, null);
                                    } else {
                                      setFieldValue(normalizedLabel, file);
                                    }
                                  }}
                                />
                                <p className="text-xs text-muted-foreground mt-1">
                                  Maximum file size: 2MB
                                </p>
                              </div>
                            ) : field.inputType === "textarea" ? (
                              <Field
                                as="textarea"
                                name={normalizedLabel}
                                rows={4}
                                className="w-full p-3 text-black dark:text-white rounded-lg bg-background border border-slate-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-all duration-200 resize-none"
                              />
                            ) : (
                              <Field
                                type={field.inputType || "text"}
                                name={normalizedLabel}
                                className="w-full p-3 text-black dark:text-white rounded-lg bg-background border border-slate-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-all duration-200"
                              />
                            )}

                            <ErrorMessage
                              name={normalizedLabel}
                              component="p"
                              className="text-red-500 text-sm mt-2 flex items-center gap-1"
                            />
                          </div>
                        )
                      })}
                    </div>

                    <div className="pt-4 animate-fade-in-delayed" style={{ animationDelay: "300ms" }}>
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full py-3 px-4 bg-blue-900 hover:bg-blue-600 text-white font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-70"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-5 w-5 animate-spin" />
                            <span>Submitting...</span>
                          </>
                        ) : (
                          <>
                            <Send className="h-5 w-5" />
                            <span>Submit Application</span>
                          </>
                        )}
                      </button>

                      <p className="text-center text-sm  mt-4">
                        By submitting this form, you agree to our Terms and Privacy Policy.
                      </p>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}